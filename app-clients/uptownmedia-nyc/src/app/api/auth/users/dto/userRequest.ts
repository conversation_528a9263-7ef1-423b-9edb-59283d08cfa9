import { validatePhoneNumber, zodPhoneNumber } from "@/utils/zodParser";
import { z } from "zod";

export const UserRequestSchema = z.object({
	name: z.string().min(1, "FirstName must be at least 1 character"),
	email: z.string().email().min(1, "email must be at least 1 character"),
	phone: z
		.string()
		.refine((value) => (value ? validatePhoneNumber(value) : true), {
			message:
				"Phone number must be in the format '(DDD) DDD-DDDD' or `DDDDDDDDDD`.",
		})
		.optional(),
	image: z.string().nullable().optional(),
});

export type UserRequest = z.infer<typeof UserRequestSchema>;
