import { z } from "zod";

import { GroupServices } from "../../services/groupsServices";

import { UserServices } from "@/app/api/auth/users/services/userServices";
import { GroupMembershipRequestSchema } from "@/app/api/group-memberships/dto/groupMembershipRequest";
import { GroupMembershipServices } from "@/app/api/group-memberships/services/groupMembershipsServices";
import { AppError } from "@/app/api/shared/errors";

const groupMembershipServices = new GroupMembershipServices();
const groupServices = new GroupServices();
const userServices = new UserServices();

export async function POST(
	request: Request,
	props: { params: Promise<{ sharedCode: string }> },
) {
	try {
		const params = await props.params;
		const body = await request.json();
		const validationData = GroupMembershipRequestSchema.parse(body);
		const group = await groupServices.getGroupBySharePassCode(
			params.sharedCode,
		);

		console.log("Group:", group);

		const newMembership = await groupMembershipServices.createGroupMembership(
			validationData,
			group.getGroupId(),
		);

		return Response.json({ newMembership }, { status: 200 });
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		return Response.json({ error: "Internal Server Error" }, { status: 500 });
	}
}
export async function GET(
	_: Request,
	props: { params: Promise<{ sharedCode: string }> },
) {
	try {
		const params = await props.params;

		const group = await groupServices.getGroupBySharePassCode(
			params.sharedCode,
		);

		const memberships =
			await groupMembershipServices.getGroupMembershipsByGroupId(
				group.getGroupId(),
			);
		const membershipsWithUserDetailsPromise = memberships.map(
			async (membership) => {
				const user = await userServices.getUserById(membership.getUserId());

				if (!user) {
					throw new AppError("User not found", 404);
				}

				return {
					...membership,
					user: {
						id: user.getUserId(),
						name: user.getName(),
						email: user.getEmail(),
					},
				};
			},
		);
		const membershipsWithUserDetails = await Promise.all(
			membershipsWithUserDetailsPromise,
		);

		return Response.json(
			{ memberships: membershipsWithUserDetails },
			{ status: 200 },
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return Response.json({ ...error.errors }, { status: 400 });
		}
		if (error instanceof AppError) {
			return Response.json(
				{ error: error.message },
				{ status: error.statusCode },
			);
		}

		console.error("Error fetching group by shared code:", error);

		return Response.json({ error: "Internal Server Error" }, { status: 500 });
	}
}
